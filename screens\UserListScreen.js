// UserListScreen.js
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';

const UserListScreen = () => {
  const [allUsers, setAllUsers] = useState([]);
  const currentUser = auth().currentUser;
  const currentUserGender = currentUser?.gender;

  useEffect(() => {
    fetchAllUsers();
  }, []);

  const fetchAllUsers = async () => {
    const oppositeGender = currentUserGender === 'male' ? 'female' : 'male';
    const snapshot = await firestore()
      .collection('users')
      .where('gender', '==', oppositeGender)
      .get();

    const fetchedUsers = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    setAllUsers(fetchedUsers);
  };

  const renderUser = ({ item }) => (
    <View style={styles.userCard}>
      <Text style={styles.userName}>{item.name}</Text>
      <Text style={styles.userDetails}>Age: {item.age}</Text>
      <Text style={styles.userDetails}>Location: {item.location}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>All Matches</Text>
      <FlatList
        data={allUsers}
        renderItem={renderUser}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.list}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  heading: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  list: {
    paddingBottom: 16,
  },
  userCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a73e8',
  },
  userDetails: {
    fontSize: 16,
    color: '#555',
  },
});

export default UserListScreen;
