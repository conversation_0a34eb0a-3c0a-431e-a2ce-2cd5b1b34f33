import { StyleSheet } from 'react-native';

// Base styles for the HomeScreen
const baseStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 10,
    paddingBottom: 0,
  },
});

// Styles for the loading and end of list components
const listStyles = StyleSheet.create({
  loadingText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: '#555',
  },
  loadingMoreContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingMoreText: {
    fontSize: 14,
    color: '#555',
  },
  endOfListContainer: {
    padding: 20,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  endOfListText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 8,
  },
  endOfListSubText: {
    fontSize: 14,
    color: '#777',
    marginBottom: 16,
    textAlign: 'center',
  },
  endOfListButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  endOfListButton: {
    backgroundColor: '#2ecc71',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 6,
    marginVertical: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  secondaryButton: {
    backgroundColor: '#3498db',
  },
  endOfListButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  }
});

// Styles for the profile completion banner
const bannerStyles = StyleSheet.create({
  bannerContainer: {
    position: 'relative',
    width: '95%',
    backgroundColor: '#fff',
    padding: 18,
    paddingRight: 30,
    marginTop: 5,
    marginBottom: 15,
    flexDirection: 'column',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 10,
    alignSelf: 'center',
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingRight: 20,
    position: 'relative',
  },
  bannerText: {
    flex: 1,
    fontSize: 15,
    color: '#333',
    lineHeight: 22,
  },
  profileLink: {
    fontSize: 15,
    color: '#007bff',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  closeIcon: {
    padding: 0,
    position: 'absolute',
    top: 0,
    right: -15,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIconText: {
    fontSize: 16,
    color: '#555',
    fontWeight: 'bold',
    lineHeight: 16,
    textAlign: 'center',
  },
  progressContainer: {
    height: 22,
    backgroundColor: '#f0f0f0',
    borderRadius: 11,
    justifyContent: 'center',
    overflow: 'hidden',
    position: 'relative',
    paddingHorizontal: 10,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 11,
  },
  progressText: {
    textAlign: 'center',
    zIndex: 1,
    fontWeight: 'bold',
    color: '#333',
    fontSize: 13,
    textShadowColor: 'rgba(255, 255, 255, 0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  }
});

// Styles for the offer popup
const popupStyles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  popupTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  popupText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#555',
    marginBottom: 25,
    textAlign: 'center',
  },
  highlightText: {
    color: '#007bff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  closeButton: {
    backgroundColor: '#007bff',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 30,
    width: '80%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#007bff',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});

// Export all styles
export { baseStyles, listStyles, bannerStyles, popupStyles };
