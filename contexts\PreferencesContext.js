import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import firestore from '@react-native-firebase/firestore';

const PreferencesContext = createContext();

export const usePreferences = () => {
    return useContext(PreferencesContext);
};

export const PreferencesProvider = ({ children }) => {
    const [preferences, setPreferences] = useState({});

    useEffect(() => {
        // Subscribe to custoPreferences regardless of auth state.
        const unsubscribe = firestore()
            .collection('custoPreferences')
            .onSnapshot(
                (querySnapshot) => {
                    const preferencesData = {};
                    querySnapshot.forEach((doc) => {
                        preferencesData[doc.id] = doc.data();
                    });
                    setPreferences(preferencesData);
                },
                (error) => {
                    console.error('Error fetching preferences in real-time:', error);
                }
            );
        return () => unsubscribe();
    }, []);

    const value = useMemo(() => ({ preferences }), [preferences]);

    return (
        <PreferencesContext.Provider value={value}>
            {children}
        </PreferencesContext.Provider>
    );
};
