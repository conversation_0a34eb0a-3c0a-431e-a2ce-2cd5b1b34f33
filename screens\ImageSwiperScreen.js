import React from 'react';
import { View, Image, Dimensions } from 'react-native';
import Swiper from 'react-native-swiper';
import styles from '../styles/ImageSwipeStyles';

const ImageSwiperScreen = ({ route }) => {
  const { images, initialIndex = 0 } = route.params;
  const screenWidth = Dimensions.get('window').width;

  return (
    <View style={styles.container}>
      <Swiper
        loop={false}
        showsPagination={true}
        dotColor="#ccc"
        activeDotColor="#007bff"
        index={initialIndex}
      >
        {images.map((imageURI, index) => (
          <View key={index} style={styles.imageContainer}>
            <Image source={{ uri: imageURI }} style={[styles.image, { width: screenWidth }]} />
          </View>
        ))}
      </Swiper>
    </View>
  );
};

export default ImageSwiperScreen;
