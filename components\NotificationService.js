import notifee, { AndroidImportance } from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import { PermissionsAndroid, Platform } from 'react-native';

// This function is no longer used - FCM handles all notifications automatically
export const displayNotification = async () => {
  // Do nothing - FCM handles notification display automatically
  console.log('displayNotification called but doing nothing - FCM handles this');
};

export const requestPermission = async () => {
  if (Platform.OS === 'android') {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
    );
  }
  await messaging().requestPermission();
};

export const createNotificationChannel = async () => {
  await notifee.createChannel({
    id: 'likedbyothers',
    name: 'Profile Likes',
    description: 'Notifications when someone likes your profile',
    importance: AndroidImportance.HIGH,
  });
};