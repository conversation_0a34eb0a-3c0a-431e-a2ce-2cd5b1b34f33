import { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';

/**
 * Custom hook to execute a callback function when a screen comes into focus
 * 
 * @param {Function} callback - Function to execute when screen comes into focus
 * @param {Array} dependencies - Dependencies array for the useEffect hook
 */
const useScreenFocus = (callback, dependencies = []) => {
  const navigation = useNavigation();

  useEffect(() => {
    // Subscribe to focus events
    const unsubscribe = navigation.addListener('focus', () => {
      // Execute the callback when the screen comes into focus
      callback();
    });

    // Clean up the listener when the component unmounts
    return unsubscribe;
  }, [navigation, callback, ...dependencies]);
};

export default useScreenFocus;
