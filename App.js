import React, { useEffect } from 'react';
import { UserInteractionsProvider } from './contexts/UserInteractionsContext';
import { UserBalanceProvider } from './contexts/UserBalanceContext';
import AppNavigator from './components/AppNavigation';
import { requestPermission, createNotificationChannel } from './components/NotificationService';
import { PreferencesProvider } from './contexts/PreferencesContext';
import messaging from '@react-native-firebase/messaging';
import auth from '@react-native-firebase/auth';
import { navigationRef } from './components/NavigationService';

const App = () => {

  useEffect(() => {
    requestPermission();
    createNotificationChannel();

    // Function to navigate to LikedByOthers tab
    const navigateToLikedByOthers = () => {
      if (navigationRef.isReady()) {
        navigationRef.reset({
          index: 0,
          routes: [
            {
              name: 'Home',
              params: { screen: 'LikedByOthers' }
            }
          ]
        });
      }
    };

    // Handle notification clicks
    const handleNotificationClick = (remoteMessage) => {
      console.log('=== FCM Notification clicked ===');
      console.log('Full remoteMessage:', JSON.stringify(remoteMessage, null, 2));

      // Check if it's a profile like notification
      const isProfileLike =
        remoteMessage?.data?.screen === 'LikedByOthers' ||
        remoteMessage?.data?.type === 'profile_like' ||
        remoteMessage?.notification?.body?.includes('liked your profile');

      console.log('isProfileLike:', isProfileLike);

      if (isProfileLike) {
        // Check if user is authenticated
        const currentUser = auth().currentUser;

        if (currentUser) {
          // User is logged in, navigate to LikedByOthers
          console.log('User is authenticated, navigating to LikedByOthers tab...');
          setTimeout(() => {
            navigateToLikedByOthers();
          }, 1500);
        } else {
          // User is not logged in, navigate to login screen with notification flag
          console.log('User is not authenticated, navigating to login screen...');
          setTimeout(() => {
            if (navigationRef.isReady()) {
              navigationRef.reset({
                index: 0,
                routes: [
                  {
                    name: 'Login',
                    params: { fromNotification: true }
                  }
                ]
              });
            }
          }, 1500);
        }
      }
    };

    // Handle notification when app is opened from quit state
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          handleNotificationClick(remoteMessage);
        }
      });

    // Handle notification when app is in background
    const unsubscribe = messaging().onNotificationOpenedApp(handleNotificationClick);

    return unsubscribe;
  }, []);

  return (
    <UserInteractionsProvider>
      <PreferencesProvider>
        <UserBalanceProvider>
          <AppNavigator />
        </UserBalanceProvider>
      </PreferencesProvider>
    </UserInteractionsProvider>
  );
};

export default App;
