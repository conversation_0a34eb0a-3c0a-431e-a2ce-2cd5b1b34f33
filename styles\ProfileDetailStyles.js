import { StyleSheet, Dimensions, Platform } from 'react-native';
import { variables } from './Variables';

const screenWidth = Dimensions.get('window').width;

// Main component styles
export const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: '#f8f9fa', 
    position: 'relative' 
  },
  scrollContainer: {
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 24,
    backgroundColor: '#f8f9fa',
  },
  profileImageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: screenWidth * 0.9,
    height: screenWidth * 1.2,
    borderRadius: 10,
    resizeMode: 'cover',
  },
  verificationBadgeProfile: {
    position: 'absolute',
    top: 0,
    left: 0,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(13, 71, 161, 0.92)',
    paddingHorizontal: 14,
    paddingVertical: 7,
    borderTopWidth: 1,
    borderBottomRightRadius: 10,
    borderTopLeftRadius: 10,
    borderTopColor: 'rgba(255, 255, 255, 0.3)',
    elevation: 8,
    shadowColor: '#0d47a1',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    zIndex: 15,
  },
  verificationTextProfile: {
    color: '#ffffff',
    fontSize: 11,
    fontWeight: '700',
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    marginLeft: 5,
    fontFamily: 'System',
  },
  overlayContainer: {
    position: 'absolute',
    bottom: 0,
    width: screenWidth * 0.9,
    borderRadius: 10,
    overflow: 'hidden',
  },
  gradient: {
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  userDetail: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 60,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  interestButtonsContainer: {
    position: 'absolute',
    bottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  interestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 25,
    paddingLeft: 20,
    paddingRight: 20,
    marginRight: 35,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  interestButtonActive: {
    color: 'red',
    fontWeight: 'bold',
  },
  interestButtonText: {
    color: '#fff',
    marginLeft: 5,
    fontWeight: '600',
  },
  clearSearchButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  favButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingLeft: 20,
    paddingRight: 20,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  favButtonText: {
    color: '#fff',
    fontSize: 25,
  },
  basicInfo: { 
    fontSize: 16, 
    color: '#34495e', 
    marginBottom: 10, 
    textAlign: 'center' 
  },
  section: {
    width: '90%',
    marginTop: 15,
    marginBottom: 5,
    backgroundColor: '#ffffff',
    borderRadius: 7,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: variables.inputTextColor,
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#4a4a4a',
    marginBottom: 8,
    lineHeight: 20,
    paddingHorizontal: 4,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingHorizontal: 4,
    alignItems: 'flex-start',
  },
  infoLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
    lineHeight: 20,
    minWidth: 120,
    marginRight: 8,
  },
  infoValue: {
    fontSize: 14,
    color: '#4a4a4a',
    lineHeight: 20,
    textTransform: 'capitalize',
    flex: 1,
  },
  phoneInfoRow: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingHorizontal: 4,
    alignItems: 'flex-start',
  },
  phoneInfoLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
    lineHeight: 20,
    minWidth: 120,
    marginRight: 8,
  },
  phoneInfoValue: {
    fontSize: 14,
    color: '#4a4a4a',
    fontWeight: '500',
    flex: 1,
  },
  viewPhoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 8,
    backgroundColor: '#f8f9fa',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  viewPhoneText: {
    fontSize: 14,
    color: '#007bff',
    fontWeight: '600',
    marginLeft: 5,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

// Payment modal styles
export const modalStyles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 400,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 0,
    maxHeight: '100%',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
      },
      android: {
        elevation: 25,
      },
    }),
  },
  closeButton: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6c757d',
    lineHeight: 18,
  },
  modalNote: {
    fontSize: 14,
    color: '#dc3545',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 10,
    backgroundColor: 'rgba(220, 53, 69, 0.1)',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(220, 53, 69, 0.2)',
    fontWeight: '500',
  },
  tabHeader: {
    flexDirection: 'row',
    marginTop: 60,
    marginHorizontal: 25,
    marginBottom: 5,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 2,
  },
  activeTab: {
    backgroundColor: '#ffffff',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6c757d',
  },
  activeTabText: {
    color: variables.themeBGColor,
  },
  tabContent: {
    paddingHorizontal: 17,
    paddingVertical: 20,
    paddingBottom: 25,
  },
  infoText: {
    fontSize: 15,
    marginBottom: 15,
    textAlign: 'left',
    color: '#2c3e50',
    fontWeight: '800',
    lineHeight: 18,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    textAlign: 'left',
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
    }),
  },
  submitButton: {
    backgroundColor: variables.themeBGColor,
    paddingVertical: 16,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...Platform.select({
      ios: {
        shadowColor: variables.themeBGColor,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  plansContainer: {
    marginTop: 15,
  },
  planCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e9ecef',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  planCardActive: {
    borderColor: variables.themeBGColor,
    transform: [{ scale: 1.02 }],
  },
  planTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  planSubtitle: {
    fontSize: 13,
    color: '#6c757d',
    fontWeight: '400',
  },
  planPriceContainer: {
    alignItems: 'flex-end',
  },
  planPrice: {
    fontSize: 20,
    color: variables.themeBGColor,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  planPriceLabel: {
    fontSize: 12,
    color: '#6c757d',
    marginTop: 2,
    fontWeight: '500',
  },
  infoSection: {
    paddingHorizontal: 5,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 5,
    borderRadius: 12,
    marginBottom: 10,
  },
  infoCardText: {
    flex: 1,
    fontSize: 13,
    color: '#374151',
    fontWeight: '500',
    lineHeight: 20,
  },
});

// Elegant Confirmation Modal Styles
export const confirmModalStyles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 0,
    width: '100%',
    maxWidth: 380,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 10 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
      },
      android: {
        elevation: 25,
      },
    }),
  },
  headerContainer: {
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 20,
    paddingHorizontal: 25,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  iconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#2c3e50',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  contentContainer: {
    paddingHorizontal: 10,
    paddingVertical: 20,
  },
  description: {
    fontSize: 14,
    color: '#555555',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  costBreakdown: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 18,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  costLabel: {
    fontSize: 15,
    color: '#6c757d',
    fontWeight: '500',
  },
  costValue: {
    fontSize: 15,
    color: '#495057',
    fontWeight: '600',
  },
  remainingRow: {
    marginBottom: 0,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#dee2e6',
  },
  remainingLabel: {
    fontSize: 16,
    color: '#2c3e50',
    fontWeight: '600',
  },
  remainingValue: {
    fontSize: 16,
    color: '#28a745',
    fontWeight: '700',
  },
  confirmText: {
    fontSize: 16,
    color: '#2c3e50',
    textAlign: 'center',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 25,
    paddingBottom: 25,
    paddingTop: 10,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6c757d',
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#007bff',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...Platform.select({
      ios: {
        shadowColor: '#007bff',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 6,
      },
    }),
  },
  confirmButtonText: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '600',
  },
});
