{"name": "Malayali Match Test", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-firebase/functions": "^21.3.0", "@react-native-firebase/messaging": "^21.3.0", "@react-native-picker/picker": "^2.9.0", "react": "18.3.1", "react-native": "0.75.3", "react-native-dropdown-picker": "^5.4.6", "react-native-fs": "^2.20.0", "react-native-image-resizer": "^1.4.5", "react-native-linear-gradient": "^2.8.3", "react-native-razorpay": "^2.3.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-swiper": "^1.6.0", "react-native-uuid": "^2.0.2", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-firebase/app": "^21.3.0", "@react-native-firebase/auth": "^21.3.0", "@react-native-firebase/firestore": "^21.3.0", "@react-native-firebase/storage": "^21.3.0", "@react-native-masked-view/masked-view": "0.3.0", "@react-native/babel-preset": "0.75.3", "@react-native/eslint-config": "0.75.3", "@react-native/metro-config": "0.75.3", "@react-native/typescript-config": "0.75.3", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "firebase": "^11.0.1", "jest": "^29.6.3", "prettier": "2.8.8", "react-dom": "18.2.0", "react-native-gesture-handler": "~2.14.0", "react-native-image-picker": "^7.2.3", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "~4.11.0", "react-native-screens": "~3.34.0", "react-native-web": "~0.19.12", "react-test-renderer": "18.3.1", "typescript": "5.0.4", "uuid": "^11.0.2"}, "engines": {"node": ">=18"}}