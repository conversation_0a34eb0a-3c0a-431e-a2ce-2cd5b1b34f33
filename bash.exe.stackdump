Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FEBA
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210285FF9, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DC0  0002100690B4 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0A0  00021006A49D (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE41C60000 ntdll.dll
7FFE41770000 KERNEL32.DLL
7FFE3F5E0000 KERNELBASE.dll
7FFE412D0000 USER32.dll
7FFE3F220000 win32u.dll
000210040000 msys-2.0.dll
7FFE41130000 GDI32.dll
7FFE3F050000 gdi32full.dll
7FFE3EFA0000 msvcp_win.dll
7FFE3F250000 ucrtbase.dll
7FFE415B0000 advapi32.dll
7FFE41160000 msvcrt.dll
7FFE40360000 sechost.dll
7FFE3FA70000 RPCRT4.dll
7FFE3E360000 CRYPTBASE.DLL
7FFE3EF00000 bcryptPrimitives.dll
7FFE41000000 IMM32.DLL
