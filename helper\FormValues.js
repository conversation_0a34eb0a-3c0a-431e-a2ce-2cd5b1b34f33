const formValues = {
    maritalStatusValues: [
        'Never married',
        'Widower',
        'Awaiting divorce',
        'Divorced',
    ],
    districtValues: [
        'Thiruvananthapuram',
        'Ko<PERSON><PERSON>',
        'Pathanam<PERSON><PERSON>',
        'Al<PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON>',
        'Ernaku<PERSON>',
        'Thrissur',
        'Palakkad',
        'Malappuram',
        'Kozhikode',
        'Wayanad',
        'Kannur',
        'Kasaragod',
    ],
    highestEducationValues: [
        "Doctorate",
        "Master's Degree",
        "Bachelor's Degree",
        "Medical UG",
        "Medical PG",
        "General Nursing",
        "Law",
        "Civil Service",
        "UGC",
        "Management / Business",
        "Finance / Chartered Courses",
        "Computer / IT",
        "Science / Engineering",
        "Arts / Media / Communication",
        "Teaching / Education",
        "ITI",
        "MS",
        "Diploma",
        "School-Level",
        "Other"
      ],
    occupationValues: [
        "Accounts/Financial Professional",
        "Actor",
        "Administrative Professional",
        "Advertising / PR Professional",
        "Airforce",
        "Air Hostess",
        "Airline Professional",
        "Animator",
        "Architect",
        "Army",
        "Artist",
        "Astrologer",
        "Auditor",
        "Banking Professional",
        "Beautician",
        "Business",
        "Business Analyst",
        "Captain",
        "Central Government Employee",
        "Chartered Accountant",
        "Chartered Financial Analyst",
        "Civil Services",
        "Clerk",
        "Company Secretary",
        "Computer Professional",
        "Computer Programmer",
        "Consultant",
        "Contractor",
        "Counsellor",
        "CTO/CEO/CFO/COO",
        "Customer Care",
        "Deacon",
        "Defence",
        "Dentist",
        "Dietician",
        "Doctor",
        "Draftsman",
        "Driver",
        "Electrician",
        "Engineer",
        "Entertainment Professional",
        "Entrepreneur",
        "Event Management Professional",
        "Executive",
        "Farmer",
        "Fashion Designer",
        "Government Staff",
        "Graphic Designer",
        "Gulf-based",
        "Hardware Professional",
        "Hotel / Hospitality Professional",
        "HR professional",
        "Industrial Designer",
        "Insurance Agent",
        "Interior Designer",
        "Journalist",
        "Lab Technician",
        "Lawyer",
        "Lecturer",
        "Legal Officer",
        "Legal professional",
        "Makeup Artist",
        "Management Professional",
        "Manager",
        "Marketing Professional",
        "Media Professional",
        "Medical Representative",
        "Merchant navy",
        "Microbiologist",
        "Navy",
        "NRI",
        "Nursing Professional",
        "Occupational Therapist",
        "Office Staff",
        "Paramedical Professional",
        "Pharmacist",
        "Physiotherapist",
        "Pilot",
        "Police",
        "Politician",
        "President/ Director/ Chairman",
        "Priest",
        "Professor",
        "Psychologist",
        "Quality Controller",
        "Sales Executive",
        "Salesman",
        "Scientist/Researcher",
        "Self Employed",
        "Social Worker",
        "Sportsman",
        "State Government Employee",
        "Student",
        "Supervisor",
        "Teacher",
        "Technical staff",
        "Trader",
        "Trainer",
        "Tutor",
        "Videographer/Photographer",
        "Web Designer",
        "Web Developer",
        "Wholesale Businessman",
        "Not employed",
        "Other"
    ],
    religionValues: [
        'Hindu',
        'Muslim',
        'Christian',
        'Jain',
        'Jewish',
        'Inter-Religion',
        'No Religion',
        'Other',
    ],
    employedInValues: [
        'Business',
        'Defence',
        'Government/PSU',
        'MNC',
        'Private',
        'Self Employed',
        'Not Working',
    ],
    salaryValues: [
        "less than 50000",
        "50000-1 lakh",
        "1-2 lakhs",
        "2-3 lakhs",
        "3-4 lakhs",
        "4-5 lakhs",
        "5-6 lakhs",
        "6-7 lakhs",
        "7-8 lakhs",
        "8-9 lakhs",
        "9-10 lakhs",
        "10-12 lakhs",
        "12-15 lakhs",
        "15-20 lakhs",
        "20-30 lakhs",
        "30-40 lakhs",
        "40-50 lakhs",
        "50-1 crore",
        "1 crore and above",
    ],
    starValues: [
        "Aswathi",
        "Bharani",
        "Karthika",
        "Rohini",
        "Makayiram",
        "Thiruvathira",
        "Punartham",
        "Pooyam",
        "Ayilyam",
        "Makam",
        "Pooram",
        "Uthram",
        "Atham",
        "Chithira",
        "Chothi",
        "Vishakom",
        "Anizham",
        "Triketta",
        "Moolam",
        "Pooradam",
        "Uthradam",
        "Thiruvonam",
        "Avittam",
        "Chathayam",
        "Poororutathy",
        "Uthratathi",
        "Revathi",
    ],
    casteData: {
        'Hindu': [
            "Select Caste",
            "Ambalavasi",
            "Brahmin",
            "Chekkala Nair",
            "Cheramar",
            "Chettiar",
            "Dheevara",
            "Ezhava",
            "Ezhuthachan",
            "Ganakar",
            "GSB",
            "InterCaste",
            "Iyer",
            "Kakkalan",
            "Kammala",
            "Kanakka",
            "Kaniyan",
            "Kartha",
            "Kshatriya/Varma",
            "Kudumbi",
            "Kurava",
            "Kurup",
            "Mannadiar",
            "Mannan",
            "Maniyani",
            "Marar",
            "Maruthuvar",
            "Menon",
            "Moothan",
            "Mudaliar",
            "Nadar",
            "Nair",
            "Naicken",
            "Nambiar",
            "Nambisan",
            "Namboothiri/Bhathathiri",
            "Pandithar",
            "Panicker",
            "Paravan",
            "Pillai",
            "Pisharody",
            "Poduval",
            "Pulaya",
            "Reddiar",
            "Saiva Vellala",
            "Saliya",
            "Sambava",
            "Saraswat Brahmin",
            "Thachar",
            "Thandan",
            "Tharakan",
            "Thiya",
            "Vaduka",
            "Vallala",
            "Vaniya",
            "Vannan",
            "Veerasaiva",
            "Velan",
            "Velar",
            "Velluthedathu Nair",
            "Vilakithala Nair",
            "Vishwakarma",
            "Viswakarma-Carpenter",
            "Viswakarma-Goldsmith",
            "Viswakarma-Blacksmith",
            "Viswakarma-Mason",
            "Warrier",
            "Guptan",
            "SC",
            "ST",
            "Other"
        ],
        'Muslim': [
            'Select Caste',
            'Sunni',
            'Shiya',
            'Mapila',
            'InterCaste',
            'Other'
        ],
        'Christian': [
            "Select Caste",
            "Basel Mission",
            "Born Again",
            "Brethren",
            "Cannonite",
            "Catholic",
            "Chaldean Syrian",
            "Cheramar",
            "Christian Nadar",
            "CNI",
            "Convert",
            "CSI",
            "Evangalical",
            "InterCaste",
            "IPC",
            "Jacobite",
            "Knanaya",
            "Knanaya Catholic",
            "Knanaya Jacobite",
            "Knanaya Pentecostal",
            "Latin Catholic",
            "Malankara",
            "Malankara Catholic",
            "Marthoma",
            "Methodist",
            "Mormon",
            "Orthodox",
            "Pentecost",
            "Presbyterian",
            "Protestant",
            "RC",
            "RCSC",
            "Salvation Army",
            "Seventh day Adventist",
            "Syrian Catholic",
            "Syro Malabar",
            "Anglo Indian",
            "SC",
            "ST",
            "Sambava",
            "Other"
        ],
    },
}

export default formValues