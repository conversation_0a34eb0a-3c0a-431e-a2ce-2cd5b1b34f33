import React, { useCallback, useState, useEffect } from 'react';
import { View, Text, FlatList, ActivityIndicator, RefreshControl } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import UserCard from '../components/UserCard';
import styles from '../styles/UserInteractionsStyles';
import useScreenFocus from '../helper/useScreenFocus';

const FavouriteScreen = () => {
  const { favourites, loading, removeFavourite, refreshUserData, silentRefreshUserData } = useUserInteractions();
  const [refreshing, setRefreshing] = useState(false);

  // Initial load - use normal refresh with loading indicator
  useEffect(() => {
    if (favourites.length === 0 && !loading) {
      refreshUserData();
    }
  }, []);

  // Pull-to-refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshUserData();
    setRefreshing(false);
  }, [refreshUserData]);

  // Create a memoized callback for silent refreshing data
  const handleSilentRefresh = useCallback(() => {
    silentRefreshUserData();
  }, [silentRefreshUserData]);

  // Silently refresh data when the screen comes into focus
  useScreenFocus(handleSilentRefresh);

  return (
    <View style={styles.container}>
      {loading && favourites.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0066cc" />
          <Text style={styles.loadingText}>Loading favourites...</Text>
        </View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={favourites}
          renderItem={({ item, index }) => (
            <UserCard
              item={item}
              users={favourites}
              index={index}
              screenName="ProfileDetailScreen"
              removeFavourite={removeFavourite}
              onPressRemove={removeFavourite}
            />
          )}
          keyExtractor={(item) => item.userUid}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={["#0066cc"]}
              tintColor="#0066cc"
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No favourites yet</Text>
              <Text style={styles.emptySubText}>Profiles you like will appear here</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

export default FavouriteScreen;
