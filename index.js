/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';

// Create a background message handler
messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log('Background message received:', remoteMessage);
    // Return early to prevent any interference with FCM's automatic notification display
    return Promise.resolve();
});

AppRegistry.registerComponent(appName, () => App);
