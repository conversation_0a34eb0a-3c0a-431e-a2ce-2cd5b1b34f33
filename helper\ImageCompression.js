import ImageResizer from 'react-native-image-resizer';
import RNFS from 'react-native-fs';

/**
 * Compresses an image if its size exceeds 1MB
 * @param {string} imageUri - The URI of the image to compress
 * @param {Function} onProgress - Optional callback to report compression progress
 * @returns {Promise<{uri: string, wasCompressed: boolean, originalSize: number, compressedSize: number}>} - Compression result
 */
export const compressImageIfNeeded = async (imageUri, onProgress = null) => {
  try {
    // Get file stats to check size
    const fileStats = await RNFS.stat(imageUri);
    const fileSizeInMB = fileStats.size / (1024 * 1024);

    console.log(`Original image size: ${fileSizeInMB.toFixed(2)} MB`);

    // If file size is greater than 1MB, compress it
    if (fileSizeInMB > 1) {
      console.log('Image size > 1MB, compressing...');

      if (onProgress) {
        onProgress('Compressing image...');
      }

      // Calculate compression quality based on file size
      let quality = 80;
      let maxWidth = 800;
      let maxHeight = 1200;

      if (fileSizeInMB > 5) {
        quality = 50;
        maxWidth = 600;
        maxHeight = 900;
      } else if (fileSizeInMB > 3) {
        quality = 60;
        maxWidth = 700;
        maxHeight = 1000;
      } else if (fileSizeInMB > 2) {
        quality = 70;
      }

      const compressedImage = await ImageResizer.createResizedImage(
        imageUri,
        maxWidth,
        maxHeight,
        'JPEG',
        quality,
        0, // rotation
        undefined, // outputPath
        false, // keep metadata
        {
          mode: 'contain',
          onlyScaleDown: true,
        }
      );

      // Check compressed file size
      const compressedStats = await RNFS.stat(compressedImage.uri);
      const compressedSizeInMB = compressedStats.size / (1024 * 1024);

      if (onProgress) {
        onProgress(`Compression complete: ${fileSizeInMB.toFixed(1)}MB → ${compressedSizeInMB.toFixed(1)}MB`);
      }

      return {
        uri: compressedImage.uri,
        wasCompressed: true,
        originalSize: fileSizeInMB,
        compressedSize: compressedSizeInMB
      };
    }

    // Return original URI if compression not needed
    return {
      uri: imageUri,
      wasCompressed: false,
      originalSize: fileSizeInMB,
      compressedSize: fileSizeInMB
    };
  } catch (error) {
    console.error('Error compressing image:', error);
    // Return original URI if compression fails
    return {
      uri: imageUri,
      wasCompressed: false,
      originalSize: 0,
      compressedSize: 0
    };
  }
};

/**
 * Gets the file size of an image in MB
 * @param {string} imageUri - The URI of the image
 * @returns {Promise<number>} - The file size in MB
 */
export const getImageSizeInMB = async (imageUri) => {
  try {
    const fileStats = await RNFS.stat(imageUri);
    return fileStats.size / (1024 * 1024);
  } catch (error) {
    console.error('Error getting image size:', error);
    return 0;
  }
};
