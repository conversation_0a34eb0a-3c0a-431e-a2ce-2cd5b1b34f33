import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { variables } from '../styles/Variables';
import { useUserBalance } from '../contexts/UserBalanceContext';

const HeaderBalance = ({ onRecharge, showRecharge = false }) => {
    const { balance, viewsAvailable, daysRemaining, isLoading, minAmount } = useUserBalance();

    if (isLoading) {
        return (
            <View style={[headerStyles.container, { justifyContent: 'center' }]}>
                <ActivityIndicator size="small" color={variables.white} />
            </View>
        );
    }

    return (
        <View style={headerStyles.container}>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>₹{balance}</Text>
                <Text style={headerStyles.labelText}>Your Balance</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>{viewsAvailable}</Text>
                <Text style={headerStyles.labelText}>Views Left</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>
                    {daysRemaining > 0 ? daysRemaining : ''}
                </Text>
                <Text style={headerStyles.labelText}>
                    {daysRemaining > 0 ? 'Days Remaining' : ''}
                </Text>
            </View>
            {balance < minAmount && showRecharge && (
                <TouchableOpacity
                    onPress={onRecharge}
                    style={headerStyles.rechargeButton}
                    activeOpacity={0.8}
                >
                    <Text style={headerStyles.rechargeText}>+ Recharge</Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

const headerStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        width: '100%',
        paddingVertical: 4,
    },
    infoContainer: {
        alignItems: 'center',
    },
    valueText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: variables.white,
        textAlign: 'center',
    },
    labelText: {
        fontSize: 10,
        color: variables.white,
        marginTop: 2,
        textAlign: 'center',
    },
    rechargeButton: {
        backgroundColor: '#28a745',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 25,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        borderWidth: 2,
        borderColor: '#ffffff40',
    },
    rechargeText: {
        color: variables.white,
        fontSize: 13,
        fontWeight: '700',
        letterSpacing: 0.5,
        textAlign: 'center',
    },
});

export default HeaderBalance;
