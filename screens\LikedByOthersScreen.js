import React, { useEffect, useCallback, useState } from 'react';
import { View, Text, FlatList, ActivityIndicator, RefreshControl } from 'react-native';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import UserCard from '../components/UserCard';
import styles from '../styles/UserInteractionsStyles';
import useScreenFocus from '../helper/useScreenFocus';

const LikedByOthersScreen = () => {
  const { likedByOthers, loading, setNotificationCount, refreshUserData, silentRefreshUserData } = useUserInteractions();
  const [refreshing, setRefreshing] = useState(false);

  // Reset notification count when screen mounts
  useEffect(() => {
    setNotificationCount(0);
  }, []);

  // Initial load - use normal refresh with loading indicator
  useEffect(() => {
    if (likedByOthers.length === 0 && !loading) {
      refreshUserData();
    }
  }, []);

  // Pull-to-refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshUserData();
    setRefreshing(false);
  }, [refreshUserData]);

  // Create a memoized callback for silent refreshing data
  const handleSilentRefresh = useCallback(() => {
    silentRefreshUserData();
  }, [silentRefreshUserData]);

  // Silently refresh data when the screen comes into focus
  useScreenFocus(handleSilentRefresh);

  return (
    <View style={styles.container}>
      {loading && likedByOthers.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0066cc" />
          <Text style={styles.loadingText}>Loading profiles...</Text>
        </View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={likedByOthers}
          renderItem={({ item, index }) => (
            <UserCard
              item={item}
              users={likedByOthers}
              index={index}
              screenName="ProfileDetailScreen"
            />
          )}
          keyExtractor={(item) => item.userUid}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={["#0066cc"]}
              tintColor="#0066cc"
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No profiles yet</Text>
              <Text style={styles.emptySubText}>Profiles who liked you will appear here</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

export default LikedByOthersScreen;
