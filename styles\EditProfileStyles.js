import { Dimensions, StyleSheet } from 'react-native'
import { variables } from './Variables'

const windowHeight = Dimensions.get('window').height;

const { themeBGColor, white, gray1, red, inputBtnWidth, black, green } = variables;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: white,
    padding: 20,
  },
  heading: {
    fontSize: 18,
    fontWeight: 'bold',
    color: variables.inputTextColor,
    marginBottom: 15,
  },
  wrapper: {
    flex: 1,
    backgroundColor: white,
  },
  label: {
    fontSize: 14,
    color: variables.inputTextColor,
    marginBottom: 5,
  },
  input: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: gray1,
    width: '100%',
  },
  inputError: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginBottom: 15,
    borderBottomWidth: 1,
    width: '100%',
    borderBottomColor: red,
  },
  errorText: {
    color: red,
    marginTop: -10,
    marginBottom: 10,
    marginLeft: 5,
    fontSize: 12,
  },
  saveButton: {
    backgroundColor: themeBGColor,
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  saveButtonText: {
    color: white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  inputContainer: {
    marginBottom: 15,
    width: '100%',
  },
  sectionContainer: {
    marginBottom: 25,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  pickerContainer: {
    borderBottomWidth: 1,
    borderBottomColor: gray1,
    marginBottom: 15,
  },
  picker: {
    height: 50,
    width: '100%',
    color: variables.inputTextColor,
  },
  charCount: {
    textAlign: 'right',
    color: '#666',
    fontSize: 12,
    marginTop: 5,
  },
  helperText: {
    fontSize: 12,
    color: '#777',
    marginTop: -10,
    marginBottom: 10,
    marginLeft: 5,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    height: windowHeight,
    zIndex: 1000
  },
});
