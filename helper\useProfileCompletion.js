import React, { useEffect, useState } from 'react';
import firestore from '@react-native-firebase/firestore';

// List of fields to check - updated to match EditProfile fields
const PROFILE_FIELDS = [
  'fullName', 'age', 'dateOfBirth', 'email', 'gender', 'phoneNumber',
  'profileImage1', 'height', 'weight', 'maritalStatus',
  'motherTongue', 'physicalStatus', 'eatingHabits', 'drinkingHabits', 'smokingHabits',
  'highestEducation', 'occupation', 'salary', 'religion', 'caste',
    'country', 'state', 'district', 'city', 'employedIn', 'aboutMe'
];

const COMPLETION_THRESHOLD = 95;

const calculateCompletion = (userData) => {
  const totalFields = PROFILE_FIELDS.length;
  let completed = 0;

  for (let i = 0; i < totalFields; i++) {
    const value = userData[PROFILE_FIELDS[i]];
    if (value !== undefined && value !== null && value.toString().trim() !== '') {
      completed++;
    }
  }

  return Math.round((completed / totalFields) * 100);
};

const useProfileCompletion = (currentUser) => {
  const [profileCompletion, setProfileCompletion] = useState(0);
  const [showProfileBanner, setShowProfileBanner] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Function to manually refresh profile completion
  const refreshProfileCompletion = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  useEffect(() => {
    if (!currentUser) return;

    const fetchProfile = async () => {
      try {
        const doc = await firestore().collection('users').doc(currentUser.uid).get();
        if (!doc.exists) return;

        const userData = doc.data();
        const percentage = calculateCompletion(userData);

        setProfileCompletion(percentage);
        setShowProfileBanner(percentage < COMPLETION_THRESHOLD);
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };

    fetchProfile();
  }, [currentUser, refreshTrigger]); // Add refreshTrigger to dependencies

  return { profileCompletion, showProfileBanner, refreshProfileCompletion };
};

export default useProfileCompletion;
