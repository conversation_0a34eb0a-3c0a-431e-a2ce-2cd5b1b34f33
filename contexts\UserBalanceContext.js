import React, { createContext, useContext, useState, useEffect } from 'react';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import { usePreferences } from './PreferencesContext';

// Create the context
const UserBalanceContext = createContext();

// Context provider component
export const UserBalanceProvider = ({ children }) => {
  const [balance, setBalance] = useState(0);
  const [viewsAvailable, setViewsAvailable] = useState(0);
  const [daysRemaining, setDaysRemaining] = useState(0);
  const [planExpiryDate, setPlanExpiryDate] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);
  const [profileData, setProfileData] = useState({});
  
  const { preferences } = usePreferences();
  const minAmount = preferences?.Payment?.minAmount || 25;

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribeAuth = auth().onAuthStateChanged((user) => {
      setCurrentUser(user);
      if (!user) {
        // User logged out, reset all states
        setBalance(0);
        setViewsAvailable(0);
        setDaysRemaining(0);
        setPlanExpiryDate(null);
        setProfileData({});
        setIsLoading(false);
      }
    });
    return () => unsubscribeAuth();
  }, []);

  // Single onSnapshot listener for user data
  useEffect(() => {
    if (!currentUser) {
      setIsLoading(false);
      return;
    }

    const userDocRef = firestore().collection('users').doc(currentUser.uid);
    const unsubscribe = userDocRef.onSnapshot(async (doc) => {
      if (doc?.exists) {
        const data = doc.data();
        
        // Update profile data for ProfileScreen
        setProfileData({
          fullName: data.fullName || '',
          age: data.age || '',
          gender: data.gender || '',
          dateOfBirth: data.dateOfBirth || '',
          email: data.email || '',
          phoneNumber: data.phoneNumber || '',
          userId: data.userId || '',
          profileImage1: data.profileImage1 || null,
          profileImage2: data.profileImage2 || null,
          profileImage3: data.profileImage3 || null,
          userVerified: data.userVerified || false,
        });

        // Calculate balance and plan information
        let paidAmount = data.paidAmount || 0;
        let remainingDays = 0;
        let expiryDate = null;
        let shouldResetPlan = false;
        let resetReason = '';

        // Calculate expiry date if active plan exists
        if (data?.activePlan && data.activePlan.purchaseDate && data.activePlan.planDays) {
          const { planDays, purchaseDate } = data.activePlan;

          try {
            expiryDate = new Date(
              purchaseDate?.toDate().getTime() + planDays * 24 * 60 * 60 * 1000
            );
            const now = new Date();

            // Calculate remaining days regardless of balance
            const timeDiff = expiryDate - now;
            remainingDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

            // Apply business logic based on requirements
            if (paidAmount > 0 && expiryDate > now) {
              // Scenario 1: Valid plan - balance > 0 AND expiry date > current date
              // Show amount, remaining days, views left (no action needed)
              console.log('Valid plan: showing balance, days, and views');
            } else if (paidAmount <= 0 && expiryDate > now) {
              // Scenario 2: Negative balance with valid expiry - reset plan
              shouldResetPlan = true;
              resetReason = 'Negative balance with valid expiry date';
              remainingDays = 0; // Reset remaining days
            } else if (paidAmount > 0 && expiryDate <= now) {
              // Scenario 3: Expired plan with positive balance - reset plan
              shouldResetPlan = true;
              resetReason = 'Plan expired with positive balance';
              remainingDays = 0; // Reset remaining days
            } else if (paidAmount <= 0 && expiryDate <= now) {
              // Additional scenario: Both expired and no/negative balance - reset plan
              shouldResetPlan = true;
              resetReason = 'Plan expired with no/negative balance';
              remainingDays = 0; // Reset remaining days
            }
          } catch (error) {
            console.error('Error calculating expiry date:', error);
            shouldResetPlan = true;
            resetReason = 'Error in date calculation';
          }
        } else if (paidAmount < 0) {
          // Handle negative balance without active plan
          shouldResetPlan = true;
          resetReason = 'Negative balance without active plan';
        } else if (paidAmount > 0 && !data?.activePlan) {
          // Handle positive balance without active plan - this is a valid state
          // User has balance but no plan, they can still use the balance for viewing contacts
          console.log('User has balance but no active plan - valid state'); 
        }

        // Reset plan if needed
        if (shouldResetPlan) {
          console.log(`Resetting plan: ${resetReason}`);
          try {
            await userDocRef.update({
              paidAmount: 0,
              activePlan: null,
            });
            // Update local state to reflect the reset
            paidAmount = 0;
            expiryDate = null;
            remainingDays = 0;
          } catch (error) {
            console.error('Error resetting plan:', error);
          }
        }

        // Update balance and plan states
        setBalance(paidAmount);
        setViewsAvailable(paidAmount > 0 ? Math.floor(paidAmount / minAmount) : 0);
        setDaysRemaining(remainingDays);
        setPlanExpiryDate(expiryDate);
      }
      setIsLoading(false);
    }, (error) => {
      console.error("Error listening to user document:", error);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [currentUser, minAmount]);

  // Method to manually refresh balance (useful for components that need to force refresh)
  const refreshBalance = async () => {
    if (!currentUser) return;

    try {
      const userDocRef = firestore().collection('users').doc(currentUser.uid);
      const doc = await userDocRef.get();
      if (doc?.exists) {
        // This will trigger the onSnapshot listener and update the state
        console.log('Manual balance refresh triggered');
      }
    } catch (error) {
      console.error('Error refreshing balance:', error);
    }
  };

  const value = {
    balance,
    viewsAvailable,
    daysRemaining,
    planExpiryDate,
    isLoading,
    currentUser,
    profileData,
    minAmount,
    refreshBalance,
  };

  return (
    <UserBalanceContext.Provider value={value}>
      {children}
    </UserBalanceContext.Provider>
  );
};

// Custom hook for consuming the context
export const useUserBalance = () => {
  const context = useContext(UserBalanceContext);
  if (!context) {
    throw new Error('useUserBalance must be used within a UserBalanceProvider');
  }
  return context;
};
