import React, { createContext, useContext, useState, useEffect } from 'react';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';

// Create the context
const UserInteractionsContext = createContext();

// Context provider component
export const UserInteractionsProvider = ({ children }) => {
  const [likedByOthers, setLikedByOthers] = useState([]);
  const [likedByOthersIds, setLikedByOthersIds] = useState([]);
  const [favourites, setFavourites] = useState([]);
  const [favouritesIds, setFavouritesIds] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [currentUsergender, setCurrentUsergender] = useState('male');
  const [viewedProfileIds, setViewedProfileIds] = useState([]);
  const [existingPreferences, setExistingPreferences] = useState({});
  const [gotRegistationOffer, setGotRegistationOffer] = useState(false);
  const [paidAmount, setpaidAmount] = useState(0);
  const [notInteresteds, setnotInteresteds] = useState([]);



  // Listen to auth state changes to know when a user is logged in
  useEffect(() => {
    const unsubscribeAuth = auth().onAuthStateChanged((user) => {
      setCurrentUserId(user ? user.uid : null);
    });
    return () => unsubscribeAuth();
  }, []);


  // Fetch user data on initial load and provide a refresh function
  useEffect(() => {
    if (!currentUserId) {
      // If no user is logged in, clear data and stop loading.
      setLikedByOthers([]);
      setLikedByOthersIds([]);
      setFavourites([]);
      setFavouritesIds([]);
      setLoading(false);
      return;
    }

    // Initial fetch of user data
    fetchUserData();
  }, [currentUserId]);

  // Function to fetch user data on demand
  const fetchUserData = async (silentRefresh = false) => {
    if (!currentUserId) return;

    // Only set loading to true if this is not a silent refresh
    if (!silentRefresh) {
      setLoading(true);
    }

    try {
      // Use get() instead of onSnapshot to reduce reads
      const doc = await firestore()
        .collection('users')
        .doc(currentUserId)
        .get();

      if (doc.exists) {
        const userData = doc.data();
        const likedIds = userData?.likedByOthers || [];
        const favIds = userData?.favourites || [];

        // Batch state updates to reduce re-renders
        const updates = {};

        // Only update state if values have changed
        if (userData?.gotRegistationOffer !== gotRegistationOffer) {
          updates.gotRegistationOffer = userData?.gotRegistationOffer || false;
        }

        if (!arraysEqual(userData?.viewedProfileIds || [], viewedProfileIds)) {
          updates.viewedProfileIds = userData?.viewedProfileIds || [];
        }

        if (userData?.gender !== currentUsergender) {
          updates.currentUsergender = userData?.gender || 'male';
        }

        // For preferences, we need a deep comparison
        const newPreferences = userData?.preferences || {};
        if (JSON.stringify(newPreferences) !== JSON.stringify(existingPreferences)) {
          updates.existingPreferences = newPreferences;
        }

        if (userData?.paidAmount !== paidAmount) {
          updates.paidAmount = userData?.paidAmount || 0;
        }

        if (!arraysEqual(userData?.notInterested || [], notInteresteds)) {
          updates.notInteresteds = userData?.notInterested || [];
        }

        // Apply all state updates at once
        if (Object.keys(updates).length > 0) {
          if (updates.gotRegistationOffer !== undefined) setGotRegistationOffer(updates.gotRegistationOffer);
          if (updates.viewedProfileIds !== undefined) setViewedProfileIds(updates.viewedProfileIds);
          if (updates.currentUsergender !== undefined) setCurrentUsergender(updates.currentUsergender);
          if (updates.existingPreferences !== undefined) setExistingPreferences(updates.existingPreferences);
          if (updates.paidAmount !== undefined) setpaidAmount(updates.paidAmount);
          if (updates.notInteresteds !== undefined) setnotInteresteds(updates.notInteresteds);
        }

        // Only fetch profiles if IDs have changed
        const likedIdsChanged = !arraysEqual(likedIds, likedByOthersIds);
        const favIdsChanged = !arraysEqual(favIds, favouritesIds);

        // If we're doing a silent refresh and nothing has changed, don't update the state
        if (silentRefresh && !likedIdsChanged && !favIdsChanged && Object.keys(updates).length === 0) {
          // Nothing changed, so we can skip the rest
          return;
        }

        // Fetch profiles in parallel if needed
        const fetchPromises = [];

        if (likedIdsChanged) {
          setLikedByOthersIds(likedIds);
          fetchPromises.push(
            fetchProfiles(likedIds).then(profiles => setLikedByOthers(profiles))
          );
        }

        if (favIdsChanged) {
          setFavouritesIds(favIds);
          fetchPromises.push(
            fetchProfiles(favIds).then(profiles => setFavourites(profiles))
          );
        }

        // Wait for all fetches to complete
        if (fetchPromises.length > 0) {
          await Promise.all(fetchPromises);
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      if (!silentRefresh) {
        setLoading(false);
      }
    }
  };

  // Helper function to compare arrays
  const arraysEqual = (a, b) => {
    if (a.length !== b.length) return false;
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();
    return sortedA.every((val, idx) => val === sortedB[idx]);
  };

  // This function has been replaced by the logic in fetchUserData

  const fetchProfiles = async (userIds) => {
    try {
      return await Promise.all(
        userIds.map(async (id) => {
          const doc = await firestore().collection('users').doc(id).get();
          return { ...doc.data(), userUid: id };
        })
      );
    } catch (error) {
      console.error('Error fetching profiles:', error);
      return [];
    }
  };

  const removeFavourite = async (userIdToRemove) => {
    if (!currentUserId) return;
    try {
      await firestore()
        .collection('users')
        .doc(currentUserId)
        .update({
          favourites: firestore.FieldValue.arrayRemove(userIdToRemove),
        });

      // Update local state immediately for better UX
      setFavourites((prev) => prev.filter((user) => user.userUid !== userIdToRemove));
      setFavouritesIds((prev) => prev.filter(id => id !== userIdToRemove));

      // Optionally refresh all data to ensure consistency
      // Uncomment if you want to refresh all data after removing a favorite
      // await fetchUserData();
    } catch (error) {
      console.error('Error removing favourite:', error);
    }
  };

  return (
    <UserInteractionsContext.Provider
      value={{
        likedByOthers,
        likedByOthersIds,
        favourites,
        favouritesIds,
        notificationCount,
        loading,
        currentUsergender,
        viewedProfileIds,
        existingPreferences,
        gotRegistationOffer,
        paidAmount,
        notInteresteds,
        removeFavourite,
        setNotificationCount,
        // Expose the refresh functions to allow components to trigger updates
        refreshUserData: fetchUserData,
        silentRefreshUserData: () => fetchUserData(true),
      }}
    >
      {children}
    </UserInteractionsContext.Provider>
  );
};

// Custom hook for consuming the context
export const useUserInteractions = () => {
  return useContext(UserInteractionsContext);
};
