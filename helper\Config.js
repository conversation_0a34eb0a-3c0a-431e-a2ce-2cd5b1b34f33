import {variables} from '../styles/Variables';
import { Ionicons } from "@expo/vector-icons";

const config = {
    globalScreenOptions : {
        headerStyle: {backgroundColor: variables.themeBGColor},
        headerTiletleStyle: {color: variables.white},
        headerTintColor: variables.white,
//        headerRight: () => ( <Ionicons name="heart-circle-sharp" size={50} color={variables.white} /> )
    },

  }
  
  export {config}