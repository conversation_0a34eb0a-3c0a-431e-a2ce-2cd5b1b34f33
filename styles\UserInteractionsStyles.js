import {StyleSheet} from 'react-native'
import {variables} from './Variables'

export default StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#ffffff',
      paddingVertical: 16,
      alignItems: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingBottom: 50,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: '#555',
    },
    emptyContainer: {
      padding: 30,
      alignItems: 'center',
      justifyContent: 'center',
      height: 300,
    },
    emptyText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#555',
      marginBottom: 8,
    },
    emptySubText: {
      fontSize: 14,
      color: '#777',
      textAlign: 'center',
    },
    flatListContent: {
      paddingBottom: 20,
    },
    favouriteText: {
      color: 'red',
      fontSize: 18,
    },
    card: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      borderRadius: 10,
      padding: 16,
      marginBottom: 12,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      position: 'relative',
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    profileImage: {
      width: 80,
      height: 80,
      borderRadius: 40,
      marginRight: 16,
    },
    detailsContainer: {
      flex: 1,
    },
    name: {
      fontSize: 18,
      fontWeight: '700',
      color: '#2c3e50',
    },
    ageLocation: {
      fontSize: 14,
      color: '#7f8c8d',
      marginTop: 4,
    },
    info: {
      fontSize: 12,
      color: '#4a4a4a',
      marginTop: 4,
    },
    removeIcon: {
      position: 'absolute',
      top: 10,
      right: 10,
    },
  });
