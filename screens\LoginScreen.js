import "react-native-gesture-handler";
import * as React from 'react';
import { Text, TextInput, TouchableOpacity, ActivityIndicator, Image, Alert, View, Platform, Linking, ScrollView, KeyboardAvoidingView } from 'react-native';
import { useState, useEffect } from "react";
import styles from '../styles/LoginStyles';
import { properties } from '../helper/Property';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import messaging from '@react-native-firebase/messaging';
import { variables } from '../styles/Variables';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { usePreferences } from '../contexts/PreferencesContext';

const LoginScreen = ({ navigation, route }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [errors, setErrors] = useState({
    email: '',
    password: '',
    server: ''
  });
  const { preferences } = usePreferences();
  const customerServiceContact = preferences?.customerService?.phoneNumber;



  // Check if user came from notification
  const cameFromNotification = route?.params?.fromNotification;

  const updateFcmTokenIfNeeded = async (user) => {
    try {
      const newToken = await messaging().getToken();
      const userDocRef = firestore().collection('users').doc(user.uid);
      const userDoc = await userDocRef.get();
      const existingToken = userDoc.data()?.fcmToken;
      if (existingToken !== newToken) {
        await userDocRef.update({ fcmToken: newToken });
      }
    } catch (error) {
      console.log('Error updating FCM token:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = auth().onAuthStateChanged(async (user) => {
      if (user) {
        await updateFcmTokenIfNeeded(user);
        // Don't auto-redirect if user came from notification - keep them on login page
        if (navigation.isFocused() && !cameFromNotification) {
          navigation.replace(properties.HOME);
        }
      } else {
        setInitialLoading(false);
      }
    });
    return unsubscribe;
  }, [cameFromNotification]);

  const validateForm = () => {
    let valid = true;
    const newErrors = {
      email: '',
      password: '',
      server: ''
    };

    if (!email.trim()) {
      newErrors.email = 'Email is required';
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      valid = false;
    }

    if (!password) {
      newErrors.password = 'Password is required';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      await auth().signInWithEmailAndPassword(email, password);
    } catch (error) {
      setLoading(false);
      setErrors({
        ...errors,
        server: 'Please verify that your email and password are correct.'
      });
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      Alert.alert("Info", "Please enter your email to reset password");
      return;
    }
    try {
      await auth().sendPasswordResetEmail(email);
      Alert.alert("Success", "Password reset email sent");
    } catch (error) {
      Alert.alert("Error", error.message);
    }
  };

  const handleHelpPress = () => {
    const phoneNumber = customerServiceContact; // Replace with actual contact number
    const url = `tel:${phoneNumber}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Phone dialer is not available on this device');
        }
      })
      .catch((err) => {
        console.error('Error opening phone dialer:', err);
        Alert.alert('Error', 'Unable to open phone dialer');
      });
  };

  // Show a loading spinner while checking auth state
  if (initialLoading) {
    return (
      <View style={styles.container}>
        <Image
          source={require('../assets/couple.jpg')}
          style={{ width: 120, height: 100, resizeMode: 'contain', borderRadius: 100, }}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* Logo Section */}
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/couple.jpg')}
              style={styles.logo}
            />
            <View style={styles.titleContainer}>
              <Image
                source={require('../assets/logo-icon.png')}
                style={styles.logoIcon}
              />
              <Text style={styles.logoTitle}>MalayaliMatch</Text>
            </View>
            <Text style={styles.logoSubHeading}>Trusted by Kerala, Loved by Malayalis!</Text>
          </View>

          {/* Show notification message if user came from notification */}
          {cameFromNotification && (
            <View style={styles.notificationMessage}>
              <Text style={styles.notificationText}>
                🔔 Please log in to view your notification content and check Liked by Others Tab
              </Text>
            </View>
          )}

          <View style={styles.formContainer}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={errors.email ? styles.inputError : styles.input}
                onChangeText={(text) => {
                  setEmail(text);
                  setErrors({ ...errors, email: '' });
                }}
                value={email}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                placeholderTextColor="#999"
              />
              {errors.email ? <Text style={styles.errorText}>{errors.email}</Text> : <View style={styles.errorPlaceholder} />}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <TextInput
                style={(errors.password || errors.server) ? styles.inputError : styles.input}
                onChangeText={(text) => {
                  setPassword(text);
                  setErrors({ ...errors, password: '', server: '' });
                }}
                value={password}
                placeholder="Enter your password"
                secureTextEntry
                placeholderTextColor="#999"
              />
              {errors.password ? (
                <Text style={styles.errorText}>{errors.password}</Text>
              ) : errors.server ? (
                <Text style={styles.errorText}>{errors.server}</Text>
              ) : (
                <View style={styles.errorPlaceholder} />
              )}
            </View>

            <TouchableOpacity onPress={handleLogin} style={styles.applogin}>
              <Text style={styles.apploginButtonText}>{loading ? "Logging in..." : "Login"}</Text>
            </TouchableOpacity>

            <View style={styles.separator}>
              <View style={styles.separatorLine} />
              <Text style={styles.separatorText}>OR</Text>
              <View style={styles.separatorLine} />
            </View>

            <TouchableOpacity onPress={() => navigation.navigate('Register')} style={styles.linkRegistered}>
              <Text style={styles.newUser}>New User? Register</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={handleForgotPassword} style={styles.link}>
              <Text style={styles.linkText}>Forgot Password?</Text>
            </TouchableOpacity>
          </View>

          {/* App Version */}
          <View style={styles.versionContainer}>
            <Text style={styles.versionText}>Version 1.0.0</Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Floating Help Button */}
      <TouchableOpacity
        style={styles.floatingHelpButton}
        onPress={handleHelpPress}
        activeOpacity={0.8}
      >
        <Icon name="phone" size={18} color="#fff" />
        <Text style={styles.helpButtonText}>Help</Text>
      </TouchableOpacity>

      {/* Full Screen Loader */}
      {loading && (
        <View style={styles.fullScreenLoader}>
          <View style={styles.loaderBackground} />
          <ActivityIndicator size="small" color={variables.themeBGColor} />
          <Text style={styles.loaderText}>Logging in...</Text>
        </View>
      )}
    </View>
  );
};

export default LoginScreen;