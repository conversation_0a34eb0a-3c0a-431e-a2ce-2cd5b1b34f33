import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { variables } from '../styles/Variables';

const RadioButton = ({ PROP, selectedGender, onGenderChange, error }) => {
  return (
    <>
      <View style={styles.radioContainer}>
        {PROP.map((option) => (
          <TouchableOpacity
            key={option.key}
            onPress={() => onGenderChange(option.key)}
            style={styles.optionContainer}
          >
            <View style={!error ? styles.radioCircle : styles.radioCircleError}>
              {selectedGender === option.key && (
                <View style={!error ? styles.selectedRb : styles.selectedRbError} />
              )}
            </View>
            <Text style={styles.radioText}>{option.text}</Text>
          </TouchableOpacity>
        ))}

      </View>
      {error && <Text style={styles.errorText}>Gender is required.</Text>}
    </>
  );
};

export default RadioButton;

const styles = StyleSheet.create({
  radioContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    marginTop: 20,
  },
  radioCircle: {
    height: 26,
    width: 26,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: variables.themeBGColor,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    marginBottom: 10,
  },
  radioCircleError: {
    height: 25,
    width: 25,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: variables.red,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  selectedRb: {
    height: 15,
    width: 15,
    borderRadius: 50,
    backgroundColor: variables.themeBGColor,
  },
  selectedRbError: {
    height: 15,
    width: 15,
    borderRadius: 50,
    backgroundColor: variables.red,
  },
  radioText: {
    fontSize: 14,
    color: variables.inputTextColor,
    marginBottom: 10,
  },
  errorText: {
    color: variables.red,
    fontSize: 12,
    marginTop: 5,
  },
});
