import { StyleSheet } from 'react-native';

export default StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
        paddingTop: 10,
    },
    searchContainer: {
        flexDirection: 'row',
        padding: 15,
        alignItems: 'center',
        backgroundColor: '#fff',
        marginHorizontal: 15,
        marginBottom: 15,
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    input: {
        flex: 1,
        height: 43,
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 15,
        backgroundColor: '#fff',
        borderColor: '#ddd',
        fontSize: 15,
    },
    searchButton: {
        marginLeft: 10,
        paddingVertical: 12,
        paddingHorizontal: 15,
        backgroundColor: '#007bff',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 2,
        minWidth: 80,
    },
    clearSearchButton: {
        position: 'absolute',
        right: 115,
        padding: 10,
        top: 13,
        zIndex: 1,
    },
    clearSearchButtonText: {
        fontSize: 18,
        color: '#999',
        fontWeight: 'bold',
    },
    applyButton: {
        padding: 12,
        paddingHorizontal: 20,
        backgroundColor: '#007bff',
        borderRadius: 8,
        alignItems: 'center',
    },
    markerStyle: {
        height: 24,
        width: 24,
        backgroundColor: '#007bff',
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#fff',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 3,
    },
    trackStyle: {
        height: 6,
        backgroundColor: '#e0e0e0',
        borderRadius: 4,
    },
    selectedStyle: {
        backgroundColor: '#007bff',
    },
    buttonText: { color: '#fff', fontWeight: 'bold' },
    clearButton: {
        padding: 12,
        paddingHorizontal: 20,
        backgroundColor: '#ff4d4d',
        borderRadius: 8,
        alignItems: 'center',
    },
    clearButtonText: { color: '#fff', fontWeight: 'bold' },
    advancedToggle: {
        alignItems: 'center',
        padding: 15,
        backgroundColor: '#fff',
        borderRadius: 8,
        elevation: 2,
        marginRight: 15,
        marginLeft: 15,
        marginBottom: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    rangeText: {
        color: '#333',
        fontSize: 14,
    },
    toggleText: {
        fontWeight: 'bold',
        color: '#007bff',
        fontSize: 15,
    },
    advancedSearchContainer: {
        backgroundColor: '#fff',
        padding: 18,
        borderRadius: 12,
        margin: 15,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 3,
    },
    filterSection: {
        marginBottom: 20,
    },
    filterLabel: {
        fontSize: 15,
        fontWeight: 'bold',
        marginBottom: 2,
        color: '#333',
    },
    filterButton: {
        backgroundColor: '#f0f0f0',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ddd',
        alignItems: 'center',
        marginTop: 5,
    },
    filterButtonText: {
        color: '#333',
        fontSize: 14,
    },
    filterButtonsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 10,
    },
    loader: { marginTop: 20 },
    listContainer: { paddingBottom: 20, position: 'relative', zIndex: 1 },
    noResultsContainer: { marginTop: 10, alignItems: 'center' },
    noResultsText: { fontSize: 16, fontWeight: 'bold', color: '#555', marginBottom: 20, },
    loadingMoreContainer: {
        padding: 20,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center'
    },
    loadingMoreText: {
        marginLeft: 10,
        fontSize: 14,
        color: '#555'
    },
    endOfListContainer: {
        padding: 20,
        alignItems: 'center'
    },
    endOfListText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#555',
        textAlign: 'center'
    },
    removeItemButton: {
        width: 24, // Fixed width for better touch target
        height: 24, // Fixed height for better touch target
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 4,
        top: 14, // Center vertically
        marginTop: -12, // Half of height to center
        backgroundColor: 'rgba(0,0,0,0.1)', // Slight background for better visibility
        borderRadius: 12, // Make it circular
        zIndex: 10, // Ensure it's above other elements
    },
    selectedItemsContainer: {
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 8, // Increased gap for better spacing
        paddingVertical: 4, // Add some padding for better visual appearance
    },
    selectedItemBadge: {
        backgroundColor: "#4caf50", // Changed to match EditPreferences styling
        borderRadius: 15,
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginRight: 8,
        marginBottom: 8,
        position: 'relative',
        paddingRight: 20, // Increased to give more space for the close button
        flexDirection: 'row',
        alignItems: 'center',
    },
    selectedItemText: {
        color: "#FFFFFF",
        fontSize: 14,
        marginRight: 5, // Add space between text and close button
    },
    removeItemText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: 20, // Helps center the 'x'
    },
    savedSearchesContainer: {
        marginBottom: 15,
        borderRadius: 10,
        padding: 12,
        backgroundColor: '#f8f9fa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
    },
    savedSearchesTitle: {
        fontSize: 15,
        fontWeight: 'bold',
        marginBottom: 10,
        color: '#333',
        textAlign: 'center',
    },
    savedSearchesLimitText: {
        fontSize: 13,
        color: '#ff4d4d',
        marginBottom: 8,
        textAlign: 'center',
    },
    savedSearchItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#fff',
        borderRadius: 8,
        marginBottom: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
        elevation: 1,
    },
    savedSearchScrollView: {
        maxHeight: 100,
        flex: 1,
    },
    savedSearchText: {
        fontSize: 13,
        color: '#555',
        flex: 1,
        lineHeight: 20,
    },
    savedSearchActions: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginLeft: 10,
        minWidth: 100,
    },
    searchButtonSmall: {
        backgroundColor: '#007bff',
        padding: 8,
        borderRadius: 5,
        width: 60,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 2,
    },
    searchButtonSmallText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',

    },
    removeSavedSearchButton: {
        padding: 0,
        backgroundColor: '#fff',
        borderRadius: 15,
        borderWidth: 1,
        borderColor: '#ff4d4d',
        width: 26,
        height: 26,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 8,
    },
    removeSavedSearchText: {
        fontSize: 14,
        color: '#ff4d4d',
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: 14,
    },
    saveButton: {
        padding: 12,
        paddingHorizontal: 20,
        backgroundColor: '#28a745',
        borderRadius: 8,
        alignItems: 'center',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 8,
        paddingTop: 85,
        paddingBottom: 20
    },
    searchButton: {
        flex: 1,
        paddingVertical: 12,
        paddingHorizontal: 10,
        backgroundColor: '#007bff',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 2,
    },
    searchButtonTop: {
        flex: 0,
        flexBasis: '25%',
        paddingVertical: 12,
        paddingHorizontal: 10,
        backgroundColor: '#007bff',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        marginLeft: 10,
        elevation: 2,
    },
    clearButton: {
        flex: 1,
        paddingVertical: 12,
        paddingHorizontal: 10,
        backgroundColor: '#ff4d4d',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 2,
    },
    saveButton: {
        flex: 1,
        paddingVertical: 12,
        paddingHorizontal: 10,
        backgroundColor: '#28a745',
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1,
        elevation: 2,
    },
    buttonText: {
        color: '#fff',
        fontWeight: '600',
        fontSize: 13,
    },
    clearButtonText: {
        color: '#fff',
        fontWeight: '600',
        fontSize: 13,
    },
    saveButtonText: {
        color: '#fff',
        fontWeight: '600',
        fontSize: 13,
    },
    dropdown: {
        borderColor: '#ddd',
        backgroundColor: '#fff',
        borderRadius: 8,
        position: 'relative',
        zIndex: -1,
    },
    dropdownContainer: {
        borderColor: '#ddd',
        backgroundColor: '#fff',
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    arrowIcon: {
        width: 20,
        height: 20,
        // Samsung-specific fixes
        ...Platform.select({
            android: {
                marginRight: 10,
            },
        }),
    },
});
