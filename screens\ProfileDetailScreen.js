import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
  Pressable,
} from 'react-native';
import Swiper from 'react-native-swiper';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { variables } from '../styles/Variables';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import functions from '@react-native-firebase/functions';
import RazorpayCheckout from 'react-native-razorpay';
import { usePreferences } from '../contexts/PreferencesContext';
import HeaderBalance from '../components/HeaderBalance';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { styles, modalStyles, confirmModalStyles } from '../styles/ProfileDetailStyles';

const ProfileDetailScreen = ({ route }) => {
  const { users, index, isFavouriteUser } = route.params;
  const navigation = useNavigation();
  const [isPhoneVisible, setIsPhoneVisible] = useState(false);
  const [isFavourite, setIsFavourite] = useState(isFavouriteUser);
  const [usersIndex, setUserIndex] = useState(index);
  const [isNotInterested, setIsNotInterested] = useState(false);

  // Modal and payment states
  const [isModalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('plans');
  const [amount, setAmount] = useState(0);
  const [selectedPlanRange, setSelectedPlanRange] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [currentUserBalance, setCurrentUserBalance] = useState(0);

  // GST calculation states
  const [isValidAmount, setIsValidAmount] = useState(false);
  const [gstAmount, setGstAmount] = useState(0);
  const [totalAmountWithGST, setTotalAmountWithGST] = useState(0);
  const [showGSTCalculation, setShowGSTCalculation] = useState(false);

  // New state: "view" vs "recharge" context.
  const [paymentContext, setPaymentContext] = useState("view");

  // Confirmation modal state
  const [isConfirmModalVisible, setConfirmModalVisible] = useState(false);
  const [confirmationData, setConfirmationData] = useState(null);

  const userId = auth().currentUser.uid;
  const userDocRef = firestore().collection('users').doc(userId);
  const selectedUserId = users[usersIndex]?.userUid;
  const { preferences } = usePreferences();
  const enableNotification = preferences?.FCMnotification?.enableNotification;
  const fetchedPlans = preferences?.Payment?.plans;
  const minAmount = preferences?.Payment?.minAmount;
  const maxAmount = preferences?.Payment?.maxAmount;
  const maxPlanDays = preferences?.Payment?.maxPlanDays;
  const enableGST = preferences?.Payment?.enableGst;
  const GSTPercentage = preferences?.Payment?.GSTPercentage;
  const APIkey = preferences?.Payment?.APIKey;


  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <HeaderBalance
          showRecharge={true}
          onRecharge={() => {
            setPaymentContext("recharge");
            setModalVisible(true);
            setActiveTab('plans');
          }}
        />
      ),
    });
  }, [navigation]);

  useEffect(() => {
    checkIfFavourite();
    checkIfNotInterested();
  }, [selectedUserId]);

  // Fetch current user's balance
  useEffect(() => {
    const fetchCurrentUserBalance = async () => {
      try {
        const userDoc = await userDocRef.get();
        const balance = userDoc.data()?.paidAmount || 0;
        setCurrentUserBalance(balance);
      } catch (error) {
        console.error("Error fetching user balance:", error);
      }
    };

    fetchCurrentUserBalance();
  }, []);

  useEffect(() => {
    async function updateViewedProfile() {
      try {
        if (selectedUserId) {
          await userDocRef.update({
            viewedProfileIds: firestore.FieldValue.arrayUnion(selectedUserId),
          });
        }
      } catch (error) {
        console.error('Error updating viewedProfileIds:', error);
      }
    }

    updateViewedProfile();
  }, [selectedUserId]);

  const checkIfFavourite = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const favourites = userDoc.data()?.favourites || [];
      setUserIndex(userIndexParam);
      setIsFavourite(favourites.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking favourite status:", error);
    }
  };

  const checkIfNotInterested = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const notInterested = userDoc.data()?.notInterested || [];
      setUserIndex(userIndexParam);
      setIsNotInterested(notInterested.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking not interested status:", error);
    }
  };

  const handleNotInterested = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      setIsNotInterested(true);
      setIsFavourite(false);
      const batch = firestore().batch();

      batch.update(userDocRef, {
        notInterested: firestore.FieldValue.arrayUnion(selectedUserId),
      });
      batch.update(userDocRef, {
        favourites: firestore.FieldValue.arrayRemove(selectedUserId),
      });
      batch.update(userDocRef, {
        likedByOthers: firestore.FieldValue.arrayRemove(userId),
      });

      await batch.commit();

    } catch (error) {
      setIsNotInterested(false);
      console.error("Error updating not interested status:", error);
    }
  };

  const sendFavouriteNotification = async (targetUserDoc) => {
    try {
      const fcmToken = targetUserDoc.data()?.fcmToken;
      const fullName = targetUserDoc.data()?.fullName;
      const userDoc = await userDocRef.get();
      const currentUserFirstName = userDoc.data()?.fullName || 'Someone';

      if (!fcmToken) return;

      const data = {
        tokens: [fcmToken],
        title: `Hello ${fullName}!`,
        body: `Awesome news! ${currentUserFirstName} just liked your profile! 💖 Take a look now!`,
        icon: 'ic_notification',
        data: {
          screen: 'LikedByOthers',
          type: 'profile_like',
        },
      };

      //functions().useEmulator('********', 5001);

      const sendNotification = functions().httpsCallable('sendNotification');
      await sendNotification(data);
    } catch (error) {
      console.error('Error calling Cloud Function:', error);
    }
  };

  const handleToggleFavourite = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      const newFavouriteState = !isFavourite;
      setIsFavourite(newFavouriteState);

      const batch = firestore().batch();

      if (newFavouriteState) {
        setIsNotInterested(false);
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayUnion(selectedUserId),
        });
        batch.update(userDocRef, {
          notInterested: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayUnion(userId),
        });

        if (enableNotification) {
          sendFavouriteNotification(targetUserDoc);
        }
      } else {
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayRemove(userId),
        });
      }

      await batch.commit();

    } catch (error) {
      setIsFavourite(!isFavourite);
      console.error("Error updating likedByOthers:", error);
    }
  };

  const updatePayment = async () => {

    if (paymentContext === "recharge") return;

    const userDoc = await userDocRef.get();
    const userNumberViewed = userDoc.data()?.numberViewedUserIds || [];
    const isAlreadyViewed = userNumberViewed.includes(users[usersIndex]?.userUid);

    if (isAlreadyViewed) {
      setIsPhoneVisible(true);
      return;
    }

    let paidAmount = userDoc.data()?.paidAmount || 0;
    // Update the current balance state to ensure consistency
    setCurrentUserBalance(paidAmount);
    if (paidAmount >= minAmount) {
      // Show elegant confirmation modal before revealing phone number
      setConfirmationData({
        paidAmount,
        minAmount,
        remainingBalance: paidAmount - minAmount,
        onConfirm: async () => {
          setConfirmModalVisible(false);
          setModalVisible(false);
          console.log("enter onConfirm");
          setIsPhoneVisible(true);
          const updatedBalance = Number(paidAmount - minAmount);
          const viewedUserUid = users[usersIndex]?.userUid;
          let updatePayload = {
            paidAmount: updatedBalance,
          };
          if (viewedUserUid !== undefined) {
            updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
          }
          await userDocRef.update(updatePayload);
          // Update the current balance state after successful update
          setCurrentUserBalance(updatedBalance);
        },
        onCancel: () => {
          setConfirmModalVisible(false);
        }
      });
      setConfirmModalVisible(true);
    } else {
      setPaymentContext("view");
      setModalVisible(true);
      setActiveTab('plans');
    }
  };

  const handleViewPhoneNumberRequest = async () => {
    updatePayment();
  };

  // GST calculation function
  const calculateGST = (baseAmount) => {
    if (!enableGST || !GSTPercentage || baseAmount <= 0) {
      setGstAmount(0);
      setTotalAmountWithGST(baseAmount);
      return { gstAmount: 0, totalAmount: baseAmount };
    }

    const gst = (baseAmount * GSTPercentage) / 100;
    const totalWithGST = baseAmount + gst;

    // Round using Math.floor to remove decimals
    const roundedGST = Math.floor(gst);
    const roundedTotal = Math.floor(totalWithGST);

    setGstAmount(roundedGST);
    setTotalAmountWithGST(roundedTotal);

    return { gstAmount: roundedGST, totalAmount: roundedTotal };
  };

  // Validation function for amount
  const validateAmount = (numericValue) => {
    let isValid = false;
    let errorMsg = "";

    if (numericValue > maxAmount) {
      errorMsg = `💸 Maximum amount allowed is ₹${maxAmount}. Please enter a smaller amount.`;
    } else if (numericValue < 0) {
      errorMsg = "💰 Please enter a valid positive amount.";
    } else if (numericValue > 0) {
      // Check if current balance + entered amount meets minimum requirement
      const totalAfterPayment = currentUserBalance + numericValue;
      if (totalAfterPayment < minAmount) {
        const remainingAmount = minAmount - currentUserBalance;
        errorMsg = `⚠️ You need ₹${remainingAmount} more to reach the minimum ₹${minAmount} required for viewing contacts.`;
      } else {
        isValid = true;
        // Calculate GST for valid amounts only if GST is enabled
        calculateGST(numericValue);
        setShowGSTCalculation(enableGST);
      }
    } else {
      // Reset GST calculation for zero or empty amounts
      setShowGSTCalculation(false);
      setGstAmount(0);
      setTotalAmountWithGST(0);
    }

    setErrorMessage(errorMsg);
    setIsValidAmount(isValid);
    return isValid;
  };

  // Helper to compute activePlan object based on updatedBalance.
  const getActivePlan = (updatedBalance) => {
    let planDays = 0;
    if (fetchedPlans) {
      for (const key in fetchedPlans) {
        if (fetchedPlans.hasOwnProperty(key)) {
          const [minRange, maxRange] = fetchedPlans[key]
            .split('-')
            .map(val => Number(val));
          if (updatedBalance >= minRange && (updatedBalance <= maxRange)) {
            planDays = Number(key);
            break;
          } else if (updatedBalance > maxAmount) {
            planDays = Number(maxPlanDays);
            break;
          }
        }
      }
    }
    return planDays > 0
      ? {
        planDays: planDays,
        planAmount: updatedBalance,
        purchaseDate: firestore.FieldValue.serverTimestamp(),
      }
      : null;
  };

  // Payment submission: differentiate behavior by paymentContext.
  const handlePayment = async () => {
    const viewedUserUid = users[usersIndex]?.userUid;
    if (amount <= 0) {
      setErrorMessage("💰 Please enter a valid amount to proceed with payment");
      return;
    }
    if (!amount || !viewedUserUid) return;

    // Use validation function instead of inline validation
    if (!isValidAmount) {
      setErrorMessage("� Please enter a valid amount to proceed with payment");
      return;
    }

    // Use total amount with GST for payment processing
    const finalPaymentAmount = enableGST ? totalAmountWithGST : amount;

    try {
      setIsLoading(true);
      setModalVisible(false);

      // functions().useEmulator('********', 5001);
      const createOrder = functions().httpsCallable('createRazorpayOrder');
      const userDoc = await userDocRef.get();
      const currrentUserId = userDoc.data()?.userId || '';
      // Use final payment amount (with GST if enabled) for order creation
      const orderResponse = await createOrder({ amount: finalPaymentAmount, userId: currrentUserId });
      const { id } = orderResponse.data;

      const options = {
        description: 'Malayali Match Test Payments',
        image: require('../assets/logo-icon.png'),
        currency: 'INR',
        key: APIkey,
        // Use final payment amount (with GST if enabled) for Razorpay
        amount: parseInt(finalPaymentAmount) * 100,
        order_id: id,
        name: 'Malayali Match Test',
        prefill: {
          email: auth().currentUser.email,
          name: auth().currentUser.displayName || ''
        },
        theme: { color: variables.themeBGColor },
        method: {
          card: true,
          netbanking: true,
          wallet: false,
          upi: true,
          emi: false,
          paylater: false
        }
      };

      RazorpayCheckout.open(options)
        .then(async (data) => {
          // functions().useEmulator('********', 5001);
          setModalVisible(false);
          const verifySignature = functions().httpsCallable('verifyRazorpaySignature');
          const verifyResponse = await verifySignature({
            razorpay_payment_id: data.razorpay_payment_id,
            razorpay_order_id: data.razorpay_order_id,
            razorpay_signature: data.razorpay_signature,
          });
          if (!verifyResponse.data.valid) {
            Alert.alert("Payment service is not available now", "Please try after some time");
            setIsLoading(false);
            return;
          }
          // Retrieve the current balance.
          let paidAmount = userDoc.data()?.paidAmount || 0;
          let updatedBalance = 0;

          if (paymentContext === "view") {
            updatedBalance = (Number(paidAmount) + Number(amount));

            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };

            // if (viewedUserUid !== undefined && updatedBalance >= minAmount) {
            //   setIsPhoneVisible(true);
            //   updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
            //   updatePayload.paidAmount = updatedBalance - minAmount;
            // }

            await userDocRef.update(updatePayload);
          } else {
            updatedBalance = Number(paidAmount) + Number(amount);
            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };
            await userDocRef.update(updatePayload);
            setPaymentContext("view");
          }

          // Close modal and reset form after successful payment
          setModalVisible(false);
          setAmount('');
          setErrorMessage('');
          setIsLoading(false);
          setShowGSTCalculation(false);
          setGstAmount(0);
          setTotalAmountWithGST(0);
          setIsValidAmount(false);

          // Fetch updated balance from Firestore to ensure accuracy
          const updatedUserDoc = await userDocRef.get();
          const newBalance = updatedUserDoc.data()?.paidAmount || 0;
          setCurrentUserBalance(newBalance);
        })
        .catch(() => {
          Alert.alert("Payment service is not available now", "Please try after some time");
          setIsLoading(false);
          // Reset form when payment is cancelled or fails
          setAmount(0);
          setErrorMessage('');
          setSelectedPlanRange(null);
          // Reset GST calculation
          setShowGSTCalculation(false);
          setGstAmount(0);
          setTotalAmountWithGST(0);
          setIsValidAmount(false);
        });
    } catch (error) {
      Alert.alert("Payment service is not available now, Please try payment after some time");
      setIsLoading(false);
      // Reset form when payment service fails
      setAmount(0);
      setErrorMessage('');
      setSelectedPlanRange(null);
      // Reset GST calculation
      setShowGSTCalculation(false);
      setGstAmount(0);
      setTotalAmountWithGST(0);
      setIsValidAmount(false);
    }
  };

  // Updated handleImagePress: filter out null image URLs.
  const handleImagePress = (item) => {
    const images = [
      item.profileImage1,
      item.profileImage2,
      item.profileImage3,
    ].filter(url => url);
    if (images.length === 0) {
      return;
    }
    navigation.navigate('ImageSwiperScreen', { images });
  };

  // Modified renderCarouselItem that accepts the slide's index.
  const renderCarouselItem = (item, slideIndex) => (
    <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <View style={styles.profileImageContainer}>
          <TouchableOpacity onPress={() => handleImagePress(item)}>
            <Image
              source={
                item.profileImage1
                  ? { uri: item.profileImage1 }
                  : require('../assets/splash.png')
              }
              style={styles.profileImage}
            />
          </TouchableOpacity>
          {item?.userVerified && (
            <View style={styles.verificationBadgeProfile}>
              <MaterialIcons name="verified" size={14} color="#ffffff" />
              <Text style={styles.verificationTextProfile}>VERIFIED</Text>
            </View>
          )}
        </View>
        <View style={styles.overlayContainer}>
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,1)']} // Reversed colors
            start={{ x: 1, y: 0 }} // Start from bottom
            end={{ x: 1, y: 1 }} // End at top
            style={styles.gradient}
          >
            <Text style={styles.userName}>{`${item.fullName}`}</Text>
            <Text style={styles.userDetail}>{`User ID: ${item.userId}`}</Text>
          </LinearGradient>
        </View>
        <View style={styles.interestButtonsContainer}>
          <TouchableOpacity style={[styles.favButton, isFavourite && styles.interestButtonActive]}
            onPress={handleToggleFavourite}
          >
            <FontAwesome name="heart" size={15} color={isFavourite ? 'red' : '#fff'} />
            <Text style={[styles.interestButtonText, isFavourite && styles.interestButtonActive]}>Interested</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.interestButton, isNotInterested && styles.interestButtonActive]}
            onPress={handleNotInterested}
            disabled={isNotInterested}
          >
            <Text style={[styles.clearSearchButtonText, isNotInterested && styles.interestButtonActive]}>✕</Text>
            <Text style={[styles.interestButtonText, isNotInterested && styles.interestButtonActive]}>Not Interested</Text>
          </TouchableOpacity>
        </View>

      </View>
      {/* About Me Section - Always show this section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="person" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>About Me</Text>
        </View>
        <Text style={styles.infoText}>{item.aboutMe || "Not updated"}</Text>
      </View>

      {/* Basic Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="info" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Basic Details</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Age:</Text>
          <Text style={styles.infoValue}>{item.age || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Date of Birth:</Text>
          <Text style={styles.infoValue}>{item.dateOfBirth || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Marital Status:</Text>
          <Text style={styles.infoValue}>{item.maritalStatus || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Gender:</Text>
          <Text style={styles.infoValue}>{item.gender || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Height:</Text>
          <Text style={styles.infoValue}>{item.height ? `${item.height} cm` : "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Weight:</Text>
          <Text style={styles.infoValue}>{item.weight ? `${item.weight} kg` : "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Mother Tongue:</Text>
          <Text style={styles.infoValue}>{item.motherTongue || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Physical Status:</Text>
          <Text style={styles.infoValue}>{item.physicalStatus || "Not updated"}</Text>
        </View>
      </View>

      {/* Religious Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="church" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Religious Details</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Religion:</Text>
          <Text style={styles.infoValue}>{
            (item.religion === 'Other' && item.customReligion) ?
              item.customReligion :
              (item.religion || "Not updated")
          }</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Caste:</Text>
          <Text style={styles.infoValue}>{
            (item.caste === 'Other' && item.customCaste) ?
              item.customCaste :
              (item.caste || "Not updated")
          }</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Star:</Text>
          <Text style={styles.infoValue}>{item.star || "Not updated"}</Text>
        </View>
      </View>

      {/* Professional Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="work" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Professional Details</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Education:</Text>
          <Text style={styles.infoValue}>{
            (item.highestEducation === 'Other' && item.customEducation) ?
              item.customEducation :
              (item.highestEducation || item.education || "Not updated")
          }</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Occupation:</Text>
          <Text style={styles.infoValue}>{
            (item.occupation === 'Other' && item.customOccupation) ?
              item.customOccupation :
              (item.occupation || "Not updated")
          }</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Salary:</Text>
          <Text style={styles.infoValue}>{item.salary || item.annualIncome || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Employed In:</Text>
          <Text style={styles.infoValue}>{
            (item.employedIn === 'Other' && item.customEmployedIn) ?
              item.customEmployedIn :
              (item.employedIn || "Not updated")
          }</Text>
        </View>
      </View>

      {/* Location Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="location-on" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Location</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Country:</Text>
          <Text style={styles.infoValue}>{item.country || 'INDIA'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>State:</Text>
          <Text style={styles.infoValue}>{item.state || 'KERALA'}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>District:</Text>
          <Text style={styles.infoValue}>{item.district || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>City:</Text>
          <Text style={styles.infoValue}>{item.city || "Not updated"}</Text>
        </View>
      </View>

      {/* Family Details Section - Always show */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="family-restroom" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Family Details</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Father:</Text>
          <Text style={styles.infoValue}>{item.father || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Mother:</Text>
          <Text style={styles.infoValue}>{item.mother || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Siblings:</Text>
          <Text style={styles.infoValue}>{item.siblings || "Not updated"}</Text>
        </View>
      </View>

      {/* Contact Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="contact-phone" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Contact Details</Text>
        </View>
        {usersIndex === slideIndex && isPhoneVisible ? (
          <View style={styles.phoneInfoRow}>
            <Text style={styles.phoneInfoLabel}>Phone:</Text>
            <Text style={styles.phoneInfoValue}>{item.phoneNumber || "Not updated"}</Text>
          </View>
        ) : (
          <TouchableOpacity onPress={handleViewPhoneNumberRequest} style={styles.viewPhoneButton}>
            <MaterialIcons name="phone" size={16} color="#007bff" />
            <Text style={styles.viewPhoneText}>View Phone Number</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Habits & Lifestyle Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="food-fork-drink" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Habits & Lifestyle</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Eating Habits:</Text>
          <Text style={styles.infoValue}>{item.eatingHabits || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Drinking Habits:</Text>
          <Text style={styles.infoValue}>{item.drinkingHabits || "Not updated"}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Smoking Habits:</Text>
          <Text style={styles.infoValue}>{item.smokingHabits || "Not updated"}</Text>
        </View>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <Swiper
        index={usersIndex}
        loop={false}
        showsPagination={false}
        dotColor="#ccc"
        activeDotColor={variables.themeBGColor}
        onIndexChanged={(newIndex) => {
          checkIfFavourite(newIndex);
          checkIfNotInterested(newIndex);
          setIsPhoneVisible(false);
        }}
      >
        {users.map((user, index) => (
          <View key={user.userId} style={{ flex: 1 }}>
            {renderCarouselItem(user, index)}
          </View>
        ))}
      </Swiper>

      {/* Modal Popup for Payment and Plans */}
      <Modal visible={isModalVisible} animationType="slide" transparent={true}>
        <View style={modalStyles.modalBackground}>
          <View style={modalStyles.modalContainer}>
            <TouchableOpacity onPress={() => {
              setModalVisible(false)
              setPaymentContext("view")
              // Reset form when modal is closed
              setAmount(0)
              setErrorMessage('')
              setSelectedPlanRange(null)
              // Reset GST calculation
              setShowGSTCalculation(false)
              setGstAmount(0)
              setTotalAmountWithGST(0)
              setIsValidAmount(false)
            }} style={modalStyles.closeButton}>
              <Text style={modalStyles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <View style={modalStyles.tabHeader}>
              <TouchableOpacity
                onPress={() => {
                  setActiveTab('plans')
                  // Reset form when switching to plans tab
                  setAmount(0)
                  setErrorMessage('')
                  setSelectedPlanRange(null)
                  // Reset GST calculation
                  setShowGSTCalculation(false)
                  setGstAmount(0)
                  setTotalAmountWithGST(0)
                  setIsValidAmount(false)
                }}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'plans' && modalStyles.activeTab,
                ]}
                activeOpacity={0.8}
              >
                <Text style={[
                  modalStyles.tabButtonText,
                  activeTab === 'plans' && modalStyles.activeTabText,
                ]}>Plans</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setActiveTab('payment')}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'payment' && modalStyles.activeTab,
                ]}
                activeOpacity={0.8}
              >
                <Text style={[
                  modalStyles.tabButtonText,
                  activeTab === 'payment' && modalStyles.activeTabText,
                ]}>Payment</Text>
              </TouchableOpacity>
            </View>
            <View style={modalStyles.tabContent}>
              {activeTab === 'plans' ? (
                <View>
                  <Text style={modalStyles.infoText}>Choose a Plan: Just ₹{minAmount} per contact</Text>
                  <View style={modalStyles.plansContainer}>
                    {fetchedPlans ? (
                      Object.keys(fetchedPlans).map((key) => {
                        const rangeStr = fetchedPlans[key]; // e.g., "25-500"
                        const [minVal, maxVal] = rangeStr.split('-').map(val => Number(val));
                        return (
                          <Pressable
                            key={key}
                            onPress={() => {
                              setSelectedPlanRange({ min: minVal, max: maxVal, planDays: Number(key) });
                              setActiveTab('payment');
                            }}
                            style={({ pressed, hovered }) => [
                              modalStyles.planCard,
                              (pressed || hovered) && modalStyles.planCardActive,
                            ]}
                          >
                            <View>
                              <Text style={modalStyles.planTitle}>{key} Days Plan</Text>
                              <Text style={modalStyles.planSubtitle}>Access to premium features</Text>
                            </View>
                            <View style={modalStyles.planPriceContainer}>
                              <Text style={modalStyles.planPrice}>₹{rangeStr}</Text>
                              <Text style={modalStyles.planPriceLabel}>Price Range</Text>
                            </View>
                          </Pressable>
                        );
                      })
                    ) : (
                      <View style={{
                        alignItems: 'center',
                        paddingVertical: 40,
                      }}>
                        <MaterialIcons name="hourglass-empty" size={32} color="#6c757d" />
                        <Text style={{
                          fontSize: 16,
                          color: '#6c757d',
                          marginTop: 12,
                          fontWeight: '500',
                        }}>Loading plans...</Text>
                      </View>
                    )}
                  </View>
                  {/* Helpful Information Messages */}
                  <View style={modalStyles.infoSection}>
                    <View style={modalStyles.infoCard}>
                      <Text style={modalStyles.infoCardText}>
                        💡 To view a contact, you need minimum ₹{minAmount}
                      </Text>
                    </View>

                    {/* GST Information Message */}
                    {enableGST && (
                      <View style={[modalStyles.infoCard]}>
                        <Text style={[modalStyles.infoCardText, { color: '#333333' }]}>
                          📋 All plan amounts are exclusive of GST. GST will be calculated during payment.
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ) : (
                <View>


                  {selectedPlanRange ? (
                    <Text style={modalStyles.infoText}>
                      Please enter your plan amount for {selectedPlanRange.planDays} days plan.
                    </Text>
                  ) : (
                    <Text style={modalStyles.infoText}>Enter your preferred amount:</Text>
                  )}
                  <TextInput
                    style={modalStyles.input}
                    placeholder="Enter amount"
                    placeholderTextColor="#999"
                    keyboardType="numeric"
                    value={amount > 0 ? amount.toString() : ''}
                    onChangeText={text => {
                      const numericValue = Number(text);
                      setAmount(numericValue);
                      // Use the existing validation function
                      validateAmount(numericValue);
                    }}
                  />
                  {errorMessage ? (
                    <Text style={{
                      color: "#dc3545",
                      marginTop: -15,
                      marginBottom: 20,
                      fontSize: 13,
                      fontWeight: '500',
                      backgroundColor: 'rgba(220, 53, 69, 0.1)',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      borderRadius: 8,
                      textAlign: 'center',
                    }}>{errorMessage}</Text>
                  ) : null}

                  {/* GST Calculation Display */}
                  {showGSTCalculation && isValidAmount && enableGST && (
                    <View style={{
                      backgroundColor: '#f8f9fa',
                      borderRadius: 12,
                      padding: 16,
                      marginBottom: 20,
                      borderWidth: 1,
                      borderColor: '#e9ecef',
                    }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: '#2c3e50',
                        marginBottom: 12,
                        textAlign: 'center',
                      }}>Payment Breakdown</Text>

                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text style={{ fontSize: 14, color: '#6c757d', fontWeight: '500' }}>Base Amount:</Text>
                        <Text style={{ fontSize: 14, color: '#2c3e50', fontWeight: '600' }}>₹{amount}</Text>
                      </View>

                      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text style={{ fontSize: 14, color: '#6c757d', fontWeight: '500' }}>GST ({GSTPercentage}%):</Text>
                        <Text style={{ fontSize: 14, color: '#2c3e50', fontWeight: '600' }}>₹{gstAmount}</Text>
                      </View>

                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingTop: 8,
                        borderTopWidth: 1,
                        borderTopColor: '#dee2e6',
                      }}>
                        <Text style={{ fontSize: 16, color: '#2c3e50', fontWeight: '700' }}>Total Amount:</Text>
                        <Text style={{ fontSize: 16, color: variables.themeBGColor, fontWeight: '700' }}>₹{totalAmountWithGST}</Text>
                      </View>
                    </View>
                  )}

                  <TouchableOpacity
                    style={[
                      modalStyles.submitButton,
                      (!isValidAmount || amount <= 0) && {
                        backgroundColor: '#6c757d',
                        opacity: 0.6,
                      }
                    ]}
                    onPress={handlePayment}
                    activeOpacity={0.8}
                    disabled={!isValidAmount || amount <= 0}
                  >
                    <MaterialIcons name="payment" size={18} color="#fff" style={{ marginRight: 8 }} />
                    <Text style={modalStyles.submitButtonText}>Submit Payment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>

      {/* Elegant Confirmation Modal for Phone Number Viewing */}
      <Modal visible={isConfirmModalVisible} animationType="fade" transparent={true}>
        <View style={confirmModalStyles.modalBackground}>
          <View style={confirmModalStyles.modalContainer}>
            {/* Header with Icon */}
            <View style={confirmModalStyles.headerContainer}>
              <View style={confirmModalStyles.iconContainer}>
                <MaterialIcons name="phone" size={32} color="#007bff" />
              </View>
              <Text style={confirmModalStyles.title}>View Phone Number</Text>
            </View>

            {/* Content */}
            <View style={confirmModalStyles.contentContainer}>
              <Text style={confirmModalStyles.description}>
                You're about to view this user's phone number.
              </Text>

              {confirmationData && (
                <View style={confirmModalStyles.costBreakdown}>
                  <View style={confirmModalStyles.costRow}>
                    <Text style={confirmModalStyles.costLabel}>Cost:</Text>
                    <Text style={confirmModalStyles.costValue}>₹{confirmationData.minAmount}</Text>
                  </View>
                  <View style={confirmModalStyles.costRow}>
                    <Text style={confirmModalStyles.costLabel}>Current Balance:</Text>
                    <Text style={confirmModalStyles.costValue}>₹{confirmationData.paidAmount}</Text>
                  </View>
                  <View style={[confirmModalStyles.costRow, confirmModalStyles.remainingRow]}>
                    <Text style={confirmModalStyles.remainingLabel}>Remaining Balance:</Text>
                    <Text style={confirmModalStyles.remainingValue}>₹{confirmationData.remainingBalance}</Text>
                  </View>
                </View>
              )}

              <Text style={confirmModalStyles.confirmText}>
                Do you want to continue?
              </Text>
            </View>

            {/* Action Buttons */}
            <View style={confirmModalStyles.buttonContainer}>
              <TouchableOpacity
                style={confirmModalStyles.cancelButton}
                onPress={confirmationData?.onCancel}
                activeOpacity={0.8}
              >
                <Text style={confirmModalStyles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={confirmModalStyles.confirmButton}
                onPress={confirmationData?.onConfirm}
                activeOpacity={0.8}
              >
                <MaterialIcons name="check" size={18} color="#fff" style={{ marginRight: 5 }} />
                <Text style={confirmModalStyles.confirmButtonText}>Continue</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={variables.themeBGColor} />
        </View>
      )}
    </View>
  );
};



export default ProfileDetailScreen;